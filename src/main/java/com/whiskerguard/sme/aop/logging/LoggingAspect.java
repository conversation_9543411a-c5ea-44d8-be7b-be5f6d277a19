package com.whiskerguard.sme.aop.logging;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.common.es.BuryPointLogDocument;
import com.whiskerguard.common.es.BuryPointLogHandle;
import com.whiskerguard.common.es.EsConstant;
import com.whiskerguard.common.util.ElasticsearchUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import tech.jhipster.config.JHipsterConstants;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.*;

/**
 * Aspect for logging execution of service and repository Spring components.
 * <p>
 * By default, it only runs with the "dev" profile.
 */
@Aspect
public class LoggingAspect {

    private final Environment env;

    ExecutorService executorService = new ThreadPoolExecutor(5, 20, 10L, TimeUnit.MINUTES, new LinkedBlockingDeque<>(100), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());

    ThreadLocal<BuryPointLogDocument> threadLocal = new ThreadLocal<>();

    @Autowired
    private ElasticsearchOperations operations;

    public LoggingAspect(Environment env) {
        this.env = env;
    }

    /**
     * Pointcut that matches all repositories, services and Web REST endpoints.
     */
    @Pointcut(
        "within(@org.springframework.stereotype.Repository *)" +
            " || within(@org.springframework.stereotype.Service *)" +
            " || within(@org.springframework.web.bind.annotation.RestController *)"
    )
    public void springBeanPointcut() {
        // Method is empty as this is just a Pointcut, the implementations are in the advices.
    }

    /**
     * Pointcut that matches all Spring beans in the application's main packages.
     */
    @Pointcut(
        "within(com.whiskerguard.sme.repository..*)" +
            " || within(com.whiskerguard.sme.service..*)" +
            " || within(com.whiskerguard.sme.web.rest..*)"
    )
    public void applicationPackagePointcut() {
        // Method is empty as this is just a Pointcut, the implementations are in the advices.
    }

    @Pointcut("within(@org.springframework.web.bind.annotation.RestController *)")
    public void controllerPointcut() {
    }

    /**
     * Retrieves the {@link Logger} associated to the given {@link JoinPoint}.
     *
     * @param joinPoint join point we want the logger for.
     * @return {@link Logger} associated to the given {@link JoinPoint}.
     */
    private Logger logger(JoinPoint joinPoint) {
        return LoggerFactory.getLogger(joinPoint.getSignature().getDeclaringTypeName());
    }

    /**
     * Advice that logs methods throwing exceptions.
     *
     * @param joinPoint join point for advice.
     * @param e         exception.
     */
    @AfterThrowing(pointcut = "applicationPackagePointcut() && springBeanPointcut()", throwing = "e")
    public void logAfterThrowing(JoinPoint joinPoint, Throwable e) {
        if (env.acceptsProfiles(Profiles.of(JHipsterConstants.SPRING_PROFILE_DEVELOPMENT))) {
            logger(joinPoint).error(
                "Exception in {}() with cause = '{}' and exception = '{}'",
                joinPoint.getSignature().getName(),
                e.getCause() != null ? e.getCause() : "NULL",
                e.getMessage(),
                e
            );
        } else {
            logger(joinPoint).error(
                "Exception in {}() with cause = {}",
                joinPoint.getSignature().getName(),
                e.getCause() != null ? String.valueOf(e.getCause()) : "NULL"
            );
        }
    }

    /**
     * Advice that logs when a method is entered and exited.
     *
     * @param joinPoint join point for advice.
     * @return result.
     * @throws Throwable throws {@link IllegalArgumentException}.
     */
    @Around("controllerPointcut()")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        Logger log = logger(joinPoint);
        try {
            HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
            executorService.submit(() -> {
                asyncDeal(request, joinPoint);
            });

            return joinPoint.proceed();
        } catch (IllegalArgumentException e) {
            log.error("Illegal argument: {} in {}()", Arrays.toString(joinPoint.getArgs()), joinPoint.getSignature().getName());
            throw e;
        }
    }

    /**
     * 异步记录日志
     *
     * @param request   HttpServletRequest
     * @param joinPoint ProceedingJoinPoint
     */
    private void asyncDeal(HttpServletRequest request, ProceedingJoinPoint joinPoint) {
        threadLocal.remove();
        threadLocal.set(new BuryPointLogDocument());
        BuryPointLogDocument buryPointLogDocument = threadLocal.get();
        BuryPointLogHandle.handle(request, buryPointLogDocument);
        //对应微服务
        buryPointLogDocument.setModule(EsConstant.MODULE_SME);

        // 获取requestBody
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Annotation[][] paramAnnotations = method.getParameterAnnotations();
        for (int i = 0; i < paramAnnotations.length; i++) {
            for (Annotation annotation : paramAnnotations[i]) {
                if (annotation instanceof RequestBody) {
                    Object arg = joinPoint.getArgs()[i];
                    String json;
                    try {
                        json = new ObjectMapper().writeValueAsString(arg);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                    buryPointLogDocument.setRequestBody(json);
                    break;
                }
            }
        }
        //判断索引是否存在
        boolean b = ElasticsearchUtil.indexExists(operations, EsConstant.INDEX_BURY_POINT_LOG);
        if (!b) {
            //创建索引
            boolean index = ElasticsearchUtil.createIndex(operations, buryPointLogDocument.getClass(), EsConstant.INDEX_BURY_POINT_LOG);
            if (!index) {
                throw new RuntimeException("创建索引失败");
            }
        }
        //保存数据
        BuryPointLogDocument save = ElasticsearchUtil.save(operations, buryPointLogDocument, EsConstant.INDEX_BURY_POINT_LOG);
        if (save == null) {
            throw new RuntimeException("保存数据失败");
        }
    }
}
