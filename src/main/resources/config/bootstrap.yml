# ===================================================================
# Spring Cloud Consul Config bootstrap configuration for the "dev" profile
# In prod profile, properties will be overwritten by the ones defined in bootstrap-prod.yml
# ===================================================================

spring:
  application:
    name: whiskerguardSmeService
  profiles:
    # The commented value for `active` can be replaced with valid Spring profiles to load.
    # Otherwise, it will be filled in by maven when building the JAR file
    # Either way, it can be overridden by `--spring.profiles.active` value passed in the commandline or `-Dspring.profiles.active` set in `JAVA_OPTS`
    active: '@spring.profiles.active@'
  cloud:
    consul:
      config:
        fail-fast: false # if not in "prod" profile, do not force to use Spring Cloud Config
        format: yaml
        profile-separator: '-'
      discovery:
        tags:
          - profile=${spring.profiles.active}
          - version='@project.version@'
          - git-version=${git.commit.id.describe:}
          - git-commit=${git.commit.id.abbrev:}
          - git-branch=${git.branch:}
          - context-path=${server.servlet.context-path:}

      host: localhost
      port: 8500
  docker:
    compose:
      enabled: false
      lifecycle-management: start-only
      file: src/main/docker/mysql.yml
