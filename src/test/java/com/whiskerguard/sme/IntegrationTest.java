package com.whiskerguard.sme;

import com.whiskerguard.sme.config.AsyncSyncConfiguration;
import com.whiskerguard.sme.config.EmbeddedRedis;
import com.whiskerguard.sme.config.EmbeddedSQL;
import com.whiskerguard.sme.config.JacksonConfiguration;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * Base composite annotation for integration tests.
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@SpringBootTest(classes = { WhiskerguardSmeServiceApp.class, JacksonConfiguration.class, AsyncSyncConfiguration.class })
@EmbeddedRedis
@EmbeddedSQL
public @interface IntegrationTest {
}
