{"generator-jhipster": {"applicationType": "microservice", "authenticationType": "jwt", "baseName": "whiskerguardSmeService", "buildTool": "maven", "cacheProvider": "redis", "clientFramework": "no", "clientTestFrameworks": null, "clientTheme": null, "creationTimestamp": 1753683761991, "databaseType": "sql", "devDatabaseType": "mysql", "enableHibernateCache": false, "enableTranslation": true, "entities": [], "feignClient": true, "jhipsterVersion": "8.10.0", "jwtSecretKey": "ODU5NzhhODZjZTNkYzAwZTQ0N2M3OGU5ZWNhNmJmNjkzYzYyNzcwMWUxZjQ0YmVkN2M3ZDRhY2JjMzdkNjkxNmM1ZjhlNjZjNTlmZjk0ODJlMDE0NzYwZWE4NDFjOTQxYzZmMzg2NWYxNTcwNWI3ZDBmMGE3OWU3MjRkNDc0NTE=", "languages": ["zh-cn", "en"], "microfrontend": null, "microfrontends": [], "nativeLanguage": "zh-cn", "packageName": "com.whiskerguard.sme", "prodDatabaseType": "mysql", "reactive": false, "serverPort": "8190", "serviceDiscoveryType": "consul", "skipClient": true, "syncUserWithIdp": null, "testFrameworks": [], "withAdminUi": null}}